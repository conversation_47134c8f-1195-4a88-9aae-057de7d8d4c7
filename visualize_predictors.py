#!/usr/bin/env python3
"""
Predictor Analysis and Visualization Script

This script analyzes the performance and distribution of different predictors
in the Giraffe pruning method and creates comprehensive visualizations.

Features:
1. Predictor usage distribution analysis
2. Layer-wise sparsity visualization
3. Performance metrics comparison
4. Predictor selection patterns
5. Sensitivity analysis visualization
"""

import re
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter
import pandas as pd
from pathlib import Path
import argparse

# Set style for better-looking plots
try:
    plt.style.use('seaborn-v0_8')
except:
    plt.style.use('seaborn')
sns.set_palette("husl")

class PredictorAnalyzer:
    """Analyzer for predictor performance and distribution"""
    
    def __init__(self, log_file="run.log"):
        self.log_file = log_file
        self.predictor_stats = {}
        self.layer_stats = []
        self.sparsity_data = []
        self.perplexity = None
        self.predictor_selections = []
        
    def parse_log_file(self):
        """Parse the log file to extract predictor and performance data"""
        print("Parsing log file...")
        
        if not Path(self.log_file).exists():
            print(f"Warning: Log file {self.log_file} not found!")
            return
            
        with open(self.log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract predictor usage statistics
        predictor_pattern = r'(\w+):\s*(\d+)层\s*\(\s*([\d.]+)%\)'
        predictor_matches = re.findall(predictor_pattern, content)
        
        for predictor, count, percentage in predictor_matches:
            self.predictor_stats[predictor] = {
                'count': int(count),
                'percentage': float(percentage)
            }
        
        # Extract layer sparsity information
        sparsity_pattern = r'layer (\d+) sparsity ([\d.]+)'
        sparsity_matches = re.findall(sparsity_pattern, content)
        
        for layer_id, sparsity in sparsity_matches:
            self.sparsity_data.append({
                'layer_id': int(layer_id),
                'sparsity': float(sparsity)
            })
        
        # Extract perplexity
        ppl_pattern = r'困惑度 \(PPL\): ([\d.]+)'
        ppl_match = re.search(ppl_pattern, content)
        if ppl_match:
            self.perplexity = float(ppl_match.group(1))
        
        # Extract predictor selection details
        selection_pattern = r'层(\d+)-([^:]+): 选择预测器=(\w+), 分数=\[([\d., ]+)\], 概率=\[([\d., ]+)\]'
        selection_matches = re.findall(selection_pattern, content)
        
        for layer_id, layer_name, predictor, scores_str, probs_str in selection_matches:
            scores = [float(x.strip()) for x in scores_str.split(',')]
            probs = [float(x.strip()) for x in probs_str.split(',')]
            
            self.predictor_selections.append({
                'layer_id': int(layer_id),
                'layer_name': layer_name.strip(),
                'selected_predictor': predictor,
                'scores': scores,
                'probabilities': probs
            })
        
        print(f"Parsed {len(self.predictor_selections)} predictor selections")
        print(f"Found {len(self.sparsity_data)} layer sparsity values")
        print(f"Perplexity: {self.perplexity}")
    
    def create_predictor_distribution_plot(self):
        """Create predictor usage distribution visualization"""
        if not self.predictor_stats:
            print("No predictor statistics found!")
            return
            
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Pie chart
        predictors = list(self.predictor_stats.keys())
        counts = [self.predictor_stats[p]['count'] for p in predictors]
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
        
        wedges, texts, autotexts = ax1.pie(counts, labels=predictors, autopct='%1.1f%%', 
                                          colors=colors, startangle=90)
        ax1.set_title('Predictor Usage Distribution', fontsize=14, fontweight='bold')
        
        # Bar chart
        ax2.bar(predictors, counts, color=colors, alpha=0.8)
        ax2.set_title('Predictor Usage Count', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Number of Layers')
        ax2.set_xlabel('Predictor Type')
        
        # Add value labels on bars
        for i, v in enumerate(counts):
            ax2.text(i, v + 0.5, str(v), ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('predictor_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_layer_sparsity_plot(self):
        """Create layer-wise sparsity visualization"""
        if not self.sparsity_data:
            print("No sparsity data found!")
            return
            
        df = pd.DataFrame(self.sparsity_data)
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        
        # Line plot of sparsity across layers
        ax1.plot(df['layer_id'], df['sparsity'], marker='o', linewidth=2, markersize=4)
        ax1.set_title('Layer-wise Sparsity Distribution', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Layer ID')
        ax1.set_ylabel('Sparsity Ratio')
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0.5, color='red', linestyle='--', alpha=0.7, label='Target Sparsity (50%)')
        ax1.legend()
        
        # Histogram of sparsity values
        ax2.hist(df['sparsity'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.set_title('Sparsity Distribution Histogram', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Sparsity Ratio')
        ax2.set_ylabel('Frequency')
        ax2.axvline(x=df['sparsity'].mean(), color='red', linestyle='--', 
                   label=f'Mean: {df["sparsity"].mean():.3f}')
        ax2.legend()
        
        plt.tight_layout()
        plt.savefig('layer_sparsity_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

    def create_predictor_selection_heatmap(self):
        """Create heatmap showing predictor selection patterns by layer type"""
        if not self.predictor_selections:
            print("No predictor selection data found!")
            return

        # Extract layer types
        layer_types = []
        for selection in self.predictor_selections:
            layer_name = selection['layer_name']
            if 'q_proj' in layer_name:
                layer_type = 'Q_Projection'
            elif 'k_proj' in layer_name:
                layer_type = 'K_Projection'
            elif 'v_proj' in layer_name:
                layer_type = 'V_Projection'
            elif 'o_proj' in layer_name:
                layer_type = 'O_Projection'
            elif 'gate_proj' in layer_name:
                layer_type = 'Gate_Projection'
            elif 'up_proj' in layer_name:
                layer_type = 'Up_Projection'
            elif 'down_proj' in layer_name:
                layer_type = 'Down_Projection'
            else:
                layer_type = 'Other'
            layer_types.append(layer_type)

        # Create DataFrame for analysis
        df_selections = pd.DataFrame(self.predictor_selections)
        df_selections['layer_type'] = layer_types

        # Create pivot table for heatmap
        pivot_table = pd.crosstab(df_selections['layer_type'],
                                 df_selections['selected_predictor'],
                                 normalize='index') * 100

        # Create heatmap
        plt.figure(figsize=(12, 8))
        sns.heatmap(pivot_table, annot=True, fmt='.1f', cmap='YlOrRd',
                   cbar_kws={'label': 'Selection Percentage (%)'})
        plt.title('Predictor Selection Patterns by Layer Type', fontsize=16, fontweight='bold')
        plt.xlabel('Selected Predictor', fontsize=12)
        plt.ylabel('Layer Type', fontsize=12)
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.savefig('predictor_selection_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()

    def create_predictor_scores_analysis(self):
        """Analyze and visualize predictor scores and probabilities"""
        if not self.predictor_selections:
            print("No predictor selection data found!")
            return

        # Extract scores for each predictor
        dolphin_scores = []
        wanda_scores = []
        snip_scores = []

        for selection in self.predictor_selections:
            scores = selection['scores']
            if len(scores) >= 3:
                dolphin_scores.append(scores[0])
                wanda_scores.append(scores[1])
                snip_scores.append(scores[2])

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # Box plot of scores
        score_data = [dolphin_scores, wanda_scores, snip_scores]
        predictor_names = ['Dolphin', 'Wanda', 'SNIP']
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

        bp = ax1.boxplot(score_data, labels=predictor_names, patch_artist=True)
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        ax1.set_title('Predictor Score Distribution', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Score Value')
        ax1.grid(True, alpha=0.3)

        # Violin plot for better distribution visualization
        parts = ax2.violinplot(score_data, positions=range(1, 4), showmeans=True)
        for i, (part, color) in enumerate(zip(parts['bodies'], colors)):
            part.set_facecolor(color)
            part.set_alpha(0.7)

        ax2.set_title('Predictor Score Density Distribution', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Score Value')
        ax2.set_xticks(range(1, 4))
        ax2.set_xticklabels(predictor_names)
        ax2.grid(True, alpha=0.3)

        # Score correlation analysis
        score_df = pd.DataFrame({
            'Dolphin': dolphin_scores,
            'Wanda': wanda_scores,
            'SNIP': snip_scores
        })

        correlation_matrix = score_df.corr()
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                   ax=ax3, square=True, cbar_kws={'label': 'Correlation'})
        ax3.set_title('Predictor Score Correlation Matrix', fontsize=14, fontweight='bold')

        # Average scores by layer depth
        layer_depths = [sel['layer_id'] for sel in self.predictor_selections]
        depth_df = pd.DataFrame({
            'layer_depth': layer_depths,
            'dolphin_score': dolphin_scores,
            'wanda_score': wanda_scores,
            'snip_score': snip_scores
        })

        # Group by layer depth ranges
        depth_df['depth_range'] = pd.cut(depth_df['layer_depth'],
                                       bins=[0, 8, 16, 24],
                                       labels=['Early (0-8)', 'Middle (9-16)', 'Late (17-24)'])

        depth_grouped = depth_df.groupby('depth_range')[['dolphin_score', 'wanda_score', 'snip_score']].mean()

        x = np.arange(len(depth_grouped.index))
        width = 0.25

        ax4.bar(x - width, depth_grouped['dolphin_score'], width, label='Dolphin', color=colors[0], alpha=0.8)
        ax4.bar(x, depth_grouped['wanda_score'], width, label='Wanda', color=colors[1], alpha=0.8)
        ax4.bar(x + width, depth_grouped['snip_score'], width, label='SNIP', color=colors[2], alpha=0.8)

        ax4.set_title('Average Predictor Scores by Layer Depth', fontsize=14, fontweight='bold')
        ax4.set_ylabel('Average Score')
        ax4.set_xlabel('Layer Depth Range')
        ax4.set_xticks(x)
        ax4.set_xticklabels(depth_grouped.index)
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('predictor_scores_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

    def create_performance_summary(self):
        """Create a comprehensive performance summary visualization"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # Performance metrics summary
        metrics = {
            'Perplexity': self.perplexity if self.perplexity else 0,
            'Total Layers': sum(self.predictor_stats[p]['count'] for p in self.predictor_stats),
            'Avg Sparsity': np.mean([s['sparsity'] for s in self.sparsity_data]) if self.sparsity_data else 0,
            'Sparsity Std': np.std([s['sparsity'] for s in self.sparsity_data]) if self.sparsity_data else 0
        }

        # Perplexity comparison (assuming baseline)
        baseline_ppl = 19.0  # Target threshold
        current_ppl = self.perplexity if self.perplexity else baseline_ppl

        categories = ['Baseline\n(Target)', 'Current\nResult']
        values = [baseline_ppl, current_ppl]
        colors = ['red' if current_ppl >= baseline_ppl else 'green', 'blue']

        bars = ax1.bar(categories, values, color=colors, alpha=0.7)
        ax1.set_title('Perplexity Comparison', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Perplexity (PPL)')
        ax1.axhline(y=baseline_ppl, color='red', linestyle='--', alpha=0.7, label='Target Threshold')

        # Add value labels on bars
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{value:.2f}', ha='center', va='bottom', fontweight='bold')

        ax1.legend()

        # Predictor efficiency analysis
        if self.predictor_stats:
            predictors = list(self.predictor_stats.keys())
            efficiencies = []

            for predictor in predictors:
                count = self.predictor_stats[predictor]['count']
                # Calculate efficiency as usage percentage / theoretical optimal (33.33%)
                efficiency = (self.predictor_stats[predictor]['percentage'] / 33.33) * 100
                efficiencies.append(efficiency)

            bars = ax2.bar(predictors, efficiencies, color=['#FF6B6B', '#4ECDC4', '#45B7D1'], alpha=0.8)
            ax2.set_title('Predictor Usage Efficiency', fontsize=14, fontweight='bold')
            ax2.set_ylabel('Efficiency (%)')
            ax2.set_xlabel('Predictor Type')
            ax2.axhline(y=100, color='red', linestyle='--', alpha=0.7, label='Balanced Usage (100%)')

            # Add value labels
            for bar, value in zip(bars, efficiencies):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 2,
                        f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')

            ax2.legend()

        # Sparsity statistics
        if self.sparsity_data:
            sparsity_values = [s['sparsity'] for s in self.sparsity_data]

            # Create histogram
            n, bins, patches = ax3.hist(sparsity_values, bins=15, alpha=0.7, color='lightblue', edgecolor='black')

            # Color bars based on target sparsity
            target_sparsity = 0.5
            for i, (patch, bin_val) in enumerate(zip(patches, bins[:-1])):
                if abs(bin_val - target_sparsity) < 0.02:  # Within 2% of target
                    patch.set_facecolor('green')
                elif abs(bin_val - target_sparsity) < 0.05:  # Within 5% of target
                    patch.set_facecolor('yellow')
                else:
                    patch.set_facecolor('red')

            ax3.axvline(x=target_sparsity, color='red', linestyle='--', linewidth=2,
                       label=f'Target: {target_sparsity:.1%}')
            ax3.axvline(x=np.mean(sparsity_values), color='blue', linestyle='-', linewidth=2,
                       label=f'Mean: {np.mean(sparsity_values):.1%}')

            ax3.set_title('Sparsity Distribution Quality', fontsize=14, fontweight='bold')
            ax3.set_xlabel('Sparsity Ratio')
            ax3.set_ylabel('Number of Layers')
            ax3.legend()

        # Summary statistics table
        ax4.axis('tight')
        ax4.axis('off')

        summary_data = [
            ['Metric', 'Value'],
            ['Final Perplexity', f'{current_ppl:.3f}' if current_ppl else 'N/A'],
            ['Target Achievement', 'PASS ✓' if current_ppl < baseline_ppl else 'FAIL ✗'],
            ['Total Pruned Layers', f"{sum(self.predictor_stats[p]['count'] for p in self.predictor_stats)}"],
            ['Average Sparsity', f"{np.mean([s['sparsity'] for s in self.sparsity_data]):.1%}" if self.sparsity_data else 'N/A'],
            ['Sparsity Std Dev', f"{np.std([s['sparsity'] for s in self.sparsity_data]):.3f}" if self.sparsity_data else 'N/A'],
            ['Dominant Predictor', max(self.predictor_stats.keys(), key=lambda x: self.predictor_stats[x]['count']) if self.predictor_stats else 'N/A'],
            ['Predictor Balance', 'Good' if max(self.predictor_stats[p]['percentage'] for p in self.predictor_stats) < 60 else 'Imbalanced' if self.predictor_stats else 'N/A']
        ]

        table = ax4.table(cellText=summary_data[1:], colLabels=summary_data[0],
                         cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 2)

        # Color code the achievement row
        if current_ppl < baseline_ppl:
            table[(2, 1)].set_facecolor('#90EE90')  # Light green
        else:
            table[(2, 1)].set_facecolor('#FFB6C1')  # Light red

        ax4.set_title('Performance Summary', fontsize=14, fontweight='bold', pad=20)

        plt.tight_layout()
        plt.savefig('performance_summary.png', dpi=300, bbox_inches='tight')
        plt.show()

    def generate_detailed_report(self):
        """Generate a detailed text report"""
        report = []
        report.append("="*80)
        report.append("GIRAFFE PREDICTOR ANALYSIS REPORT")
        report.append("="*80)
        report.append("")

        # Performance Summary
        report.append("PERFORMANCE SUMMARY:")
        report.append("-" * 40)
        if self.perplexity:
            report.append(f"Final Perplexity: {self.perplexity:.4f}")
            report.append(f"Target Achievement: {'PASS ✓' if self.perplexity < 19.0 else 'FAIL ✗'}")

        if self.sparsity_data:
            sparsity_values = [s['sparsity'] for s in self.sparsity_data]
            report.append(f"Average Sparsity: {np.mean(sparsity_values):.1%}")
            report.append(f"Sparsity Std Dev: {np.std(sparsity_values):.4f}")
            report.append(f"Min Sparsity: {np.min(sparsity_values):.1%}")
            report.append(f"Max Sparsity: {np.max(sparsity_values):.1%}")

        report.append("")

        # Predictor Statistics
        if self.predictor_stats:
            report.append("PREDICTOR USAGE STATISTICS:")
            report.append("-" * 40)
            total_layers = sum(self.predictor_stats[p]['count'] for p in self.predictor_stats)

            for predictor, stats in self.predictor_stats.items():
                report.append(f"{predictor.upper():>12}: {stats['count']:>3} layers ({stats['percentage']:>5.1f}%)")

            report.append(f"{'TOTAL':>12}: {total_layers:>3} layers")

            # Find dominant predictor
            dominant = max(self.predictor_stats.keys(), key=lambda x: self.predictor_stats[x]['count'])
            report.append(f"Dominant Predictor: {dominant.upper()}")

        report.append("")

        # Layer Type Analysis
        if self.predictor_selections:
            report.append("LAYER TYPE ANALYSIS:")
            report.append("-" * 40)

            layer_type_counts = defaultdict(lambda: defaultdict(int))
            for selection in self.predictor_selections:
                layer_name = selection['layer_name']
                predictor = selection['selected_predictor']

                if 'q_proj' in layer_name:
                    layer_type = 'Q_Projection'
                elif 'k_proj' in layer_name:
                    layer_type = 'K_Projection'
                elif 'v_proj' in layer_name:
                    layer_type = 'V_Projection'
                elif 'o_proj' in layer_name:
                    layer_type = 'O_Projection'
                elif 'gate_proj' in layer_name:
                    layer_type = 'Gate_Projection'
                elif 'up_proj' in layer_name:
                    layer_type = 'Up_Projection'
                elif 'down_proj' in layer_name:
                    layer_type = 'Down_Projection'
                else:
                    layer_type = 'Other'

                layer_type_counts[layer_type][predictor] += 1

            for layer_type, predictor_counts in layer_type_counts.items():
                total = sum(predictor_counts.values())
                report.append(f"\n{layer_type}:")
                for predictor, count in predictor_counts.items():
                    percentage = (count / total) * 100
                    report.append(f"  {predictor}: {count}/{total} ({percentage:.1f}%)")

        report.append("")
        report.append("="*80)

        # Save report
        with open('predictor_analysis_report.txt', 'w') as f:
            f.write('\n'.join(report))

        print('\n'.join(report))

    def run_complete_analysis(self):
        """Run the complete analysis and generate all visualizations"""
        print("Starting Giraffe Predictor Analysis...")
        print("="*50)

        # Parse log file
        self.parse_log_file()

        # Generate all visualizations
        print("\n1. Creating predictor distribution plots...")
        self.create_predictor_distribution_plot()

        print("2. Creating layer sparsity analysis...")
        self.create_layer_sparsity_plot()

        print("3. Creating predictor selection heatmap...")
        self.create_predictor_selection_heatmap()

        print("4. Creating predictor scores analysis...")
        self.create_predictor_scores_analysis()

        print("5. Creating performance summary...")
        self.create_performance_summary()

        print("6. Generating detailed report...")
        self.generate_detailed_report()

        print("\nAnalysis complete! Generated files:")
        print("- predictor_distribution.png")
        print("- layer_sparsity_analysis.png")
        print("- predictor_selection_heatmap.png")
        print("- predictor_scores_analysis.png")
        print("- performance_summary.png")
        print("- predictor_analysis_report.txt")


def main():
    parser = argparse.ArgumentParser(description='Analyze Giraffe predictor performance and distribution')
    parser.add_argument('--log-file', default='run.log', help='Path to the log file')
    parser.add_argument('--output-dir', default='.', help='Output directory for plots and reports')

    args = parser.parse_args()

    # Change to output directory
    if args.output_dir != '.':
        Path(args.output_dir).mkdir(exist_ok=True)
        import os
        os.chdir(args.output_dir)

    # Run analysis
    analyzer = PredictorAnalyzer(args.log_file)
    analyzer.run_complete_analysis()


if __name__ == "__main__":
    main()
